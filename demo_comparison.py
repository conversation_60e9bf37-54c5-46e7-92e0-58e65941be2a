#!/usr/bin/env python3
"""
Demo script to showcase the Gemini client comparison functionality.
This script runs a quick comparison test and displays the results.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tests.test_gemini_client_comparison import GeminiClientComparator


def main():
    """Run a demonstration of the Gemini client comparison."""
    
    print("🔍 Gemini Client Comparison Demo")
    print("=" * 50)
    
    # Check prerequisites
    if not os.path.exists("hkbanknote.png"):
        print("❌ Error: hkbanknote.png not found")
        return 1
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ Error: GEMINI_API_KEY not set")
        print("Please set your API key: export GEMINI_API_KEY='your-key'")
        return 1
    
    print("✅ Prerequisites met")
    print()
    
    try:
        # Initialize comparator
        print("Initializing Gemini client comparator...")
        comparator = GeminiClientComparator(api_key)
        
        # Load test image
        print("Loading test image...")
        image = comparator.load_test_image("hkbanknote10.png")
        
        # Run a few comparison iterations
        print("Running 3 comparison iterations...")
        print("-" * 30)
        
        results = []
        for i in range(1, 4):
            print(f"\nIteration {i}:")
            result = comparator.compare_single_iteration(image, "high_accuracy", i)
            results.append(result)
            
            print(f"  Client 1: {'✅ Success' if result.success_client1 else '❌ Failed'} "
                  f"({result.processing_time_client1:.2f}s)")
            print(f"  Client 2: {'✅ Success' if result.success_client2 else '❌ Failed'} "
                  f"({result.processing_time_client2:.2f}s)")
            print(f"  Results match: {'✅ Yes' if result.results_match else '❌ No'}")
            
            if result.error_client1:
                print(f"  Client 1 error: {result.error_client1}")
            if result.error_client2:
                print(f"  Client 2 error: {result.error_client2}")
        
        # Generate summary
        print("\n" + "=" * 50)
        print("DEMO SUMMARY")
        print("=" * 50)
        
        summary = comparator.generate_summary(results)
        
        print(f"Total iterations: {summary.total_iterations}")
        print(f"Client 1 success rate: {summary.successful_client1}/{summary.total_iterations} "
              f"({(summary.successful_client1/summary.total_iterations)*100:.1f}%)")
        print(f"Client 2 success rate: {summary.successful_client2}/{summary.total_iterations} "
              f"({(summary.successful_client2/summary.total_iterations)*100:.1f}%)")
        print(f"Result consistency: {summary.matching_results}/{summary.total_iterations} "
              f"({summary.consistency_rate:.1f}%)")
        print(f"Avg time Client 1: {summary.avg_time_client1:.2f}s")
        print(f"Avg time Client 2: {summary.avg_time_client2:.2f}s")
        
        # Performance comparison
        if summary.avg_time_client1 > 0 and summary.avg_time_client2 > 0:
            speed_diff = abs(summary.avg_time_client1 - summary.avg_time_client2)
            faster_client = "Client 1" if summary.avg_time_client1 < summary.avg_time_client2 else "Client 2"
            print(f"Performance: {faster_client} is {speed_diff:.2f}s faster on average")
        
        # Save results
        results_file = comparator.save_detailed_results(results, "_demo")
        print(f"\nDetailed results saved to: {results_file}")
        
        print("\n🎉 Demo completed successfully!")
        print("\nTo run more comprehensive tests, use:")
        print("  python run_gemini_comparison.py")
        print("  python tests/test_gemini_client_comparison.py --quick")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
