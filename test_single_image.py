#!/usr/bin/env python3
"""Test single image and display recognition results."""

import sys
import os
from src.currency_recognizer import CurrencyRecognizer
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_image_with_details(image_path):
    """Test image and show detailed results."""
    recognizer = CurrencyRecognizer()
    
    # Test configurations
    configs = [
        ("No Preprocessing - High Accuracy", "high_accuracy_local", False),
        ("Full Preprocessing - High Accuracy", "high_accuracy_local", True),
        ("Web Optimized - Balanced", "balanced_web", True),
        ("Conservative Mode", "conservative", True)
    ]
    
    print(f"\nTesting image: {image_path}")
    print("=" * 80)
    
    for config_name, preset, preprocess in configs:
        print(f"\n{config_name}:")
        print("-" * 40)
        
        try:
            result = recognizer.recognize_currency(
                image_path,
                preset_name=preset,
                enable_preprocessing=preprocess
            )
            
            if result.success:
                print(f"Success! (Processing time: {result.processing_time:.2f}s)")
                print(f"Analysis:")
                print(json.dumps(result.analysis, indent=2, ensure_ascii=False))
            else:
                print(f"Failed: {result.error_message}")
                
        except Exception as e:
            print(f"Error: {str(e)}")
        
        print()

if __name__ == "__main__":
    test_image_with_details("hkbanknote5.png")