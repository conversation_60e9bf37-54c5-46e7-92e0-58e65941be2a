"""
Module for saving currency recognition results in various formats.
"""

import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from .currency_recognizer import RecognitionResult


class ResultSaver:
    """Saves currency recognition results in various formats."""
    
    def __init__(self, output_dir: str = "test_results"):
        """
        Initialize the result saver.
        
        Args:
            output_dir: Directory to save results
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def save_single_result(self, 
                          result: RecognitionResult,
                          image_name: str,
                          format: str = "summary",
                          filename: Optional[str] = None) -> str:
        """
        Save a single recognition result.
        
        Args:
            result: RecognitionResult object
            image_name: Name of the image analyzed
            format: Output format ('summary', 'detailed', 'json')
            filename: Custom filename (auto-generated if None)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"recognition_result_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        if format == "json":
            self._save_json_result(result, image_name, filepath.with_suffix('.json'))
        else:
            content = self._format_result(result, image_name, format)
            filepath.write_text(content, encoding='utf-8')
        
        return str(filepath)
    
    def save_batch_results(self,
                          results: List[RecognitionResult],
                          image_names: List[str],
                          filename: Optional[str] = None) -> str:
        """
        Save multiple recognition results in a single consolidated file.
        
        Args:
            results: List of RecognitionResult objects
            image_names: List of image names
            filename: Custom filename (auto-generated if None)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_recognition_results_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        content = self._format_batch_results(results, image_names)
        filepath.write_text(content, encoding='utf-8')
        
        return str(filepath)
    
    def save_consolidated_summary(self,
                                 results: Union[RecognitionResult, List[RecognitionResult]],
                                 image_names: Union[str, List[str]],
                                 filename: Optional[str] = None) -> str:
        """
        Save a consolidated summary in the format: "5 HK-$20" etc.
        
        Args:
            results: Single result or list of results
            image_names: Single image name or list of names
            filename: Custom filename (auto-generated if None)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"currency_summary_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        # Ensure results and image_names are lists
        if not isinstance(results, list):
            results = [results]
        if not isinstance(image_names, list):
            image_names = [image_names]
        
        content = self._format_consolidated_summary(results, image_names)
        filepath.write_text(content, encoding='utf-8')
        
        return str(filepath)
    
    def _format_result(self, result: RecognitionResult, image_name: str, format: str) -> str:
        """Format a single result for text output."""
        lines = []
        lines.append(f"Currency Recognition Result")
        lines.append(f"{'=' * 60}")
        lines.append(f"Image: {image_name}")
        lines.append(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Status: {'Success' if result.success else 'Failed'}")
        lines.append(f"Processing Time: {result.processing_time:.2f}s")
        lines.append(f"Configuration: {result.config_used}")
        
        if result.error_message:
            lines.append(f"Error: {result.error_message}")
            return "\n".join(lines)
        
        if result.success and result.analysis:
            lines.append("")
            lines.append("Recognition Results:")
            lines.append("-" * 30)
            
            # Format currency summary
            summary = self._extract_currency_summary(result.analysis)
            if summary:
                lines.append(f"Summary: {summary}")
                lines.append("")
            
            if format == "detailed":
                # Add detailed breakdown
                banknotes = result.analysis.get("紙幣", [])
                if banknotes:
                    lines.append("Banknotes:")
                    for item in banknotes:
                        lines.append(f"  - {item.get('面額', 'Unknown')}: {item.get('數量', 0)} "
                                   f"(confidence: {item.get('置信度', 0):.2f})")
                
                coins = result.analysis.get("硬幣", [])
                if coins:
                    lines.append("Coins:")
                    for item in coins:
                        lines.append(f"  - {item.get('面額', 'Unknown')}: {item.get('數量', 0)} "
                                   f"(confidence: {item.get('置信度', 0):.2f})")
                
                unidentified = result.analysis.get("無法識別", {})
                if unidentified:
                    lines.append("Unidentified:")
                    lines.append(f"  - Reason: {unidentified.get('原因', 'Unknown')}")
                    lines.append(f"  - Features: {unidentified.get('可見特徵', 'None')}")
        
        return "\n".join(lines)
    
    def _format_batch_results(self, results: List[RecognitionResult], image_names: List[str]) -> str:
        """Format multiple results for text output."""
        lines = []
        lines.append(f"Batch Currency Recognition Results")
        lines.append(f"{'=' * 60}")
        lines.append(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append(f"Total Images: {len(results)}")
        lines.append(f"Successful: {sum(1 for r in results if r.success)}")
        lines.append("")
        
        # Individual results
        for i, (result, image_name) in enumerate(zip(results, image_names)):
            lines.append(f"[{i+1}] {image_name}")
            
            if result.success and result.analysis:
                summary = self._extract_currency_summary(result.analysis)
                lines.append(f"    Result: {summary if summary else 'No currency detected'}")
            else:
                lines.append(f"    Result: Failed - {result.error_message or 'Unknown error'}")
            
            lines.append(f"    Time: {result.processing_time:.2f}s")
            lines.append("")
        
        # Total summary
        lines.append("-" * 60)
        lines.append("Combined Summary:")
        combined_summary = self._format_consolidated_summary(results, image_names)
        lines.extend(combined_summary.split('\n')[2:])  # Skip header lines
        
        return "\n".join(lines)
    
    def _format_consolidated_summary(self, results: List[RecognitionResult], image_names: List[str]) -> str:
        """Format consolidated summary in the requested format."""
        lines = []
        lines.append(f"Currency Recognition Summary")
        lines.append(f"{'=' * 60}")
        
        # Aggregate all currency
        total_banknotes = {}
        total_coins = {}
        
        for result in results:
            if result.success and result.analysis:
                # Process banknotes
                banknotes = result.analysis.get("紙幣", [])
                for item in banknotes:
                    denomination = item.get("面額", "Unknown")
                    quantity = item.get("數量", 0)
                    if denomination in total_banknotes:
                        total_banknotes[denomination] += quantity
                    else:
                        total_banknotes[denomination] = quantity
                
                # Process coins
                coins = result.analysis.get("硬幣", [])
                for item in coins:
                    denomination = item.get("面額", "Unknown")
                    quantity = item.get("數量", 0)
                    if denomination in total_coins:
                        total_coins[denomination] += quantity
                    else:
                        total_coins[denomination] = quantity
        
        # Format summary in requested style
        summary_parts = []
        
        # Add banknotes
        for denomination, quantity in sorted(total_banknotes.items(), 
                                           key=lambda x: self._extract_value(x[0]), 
                                           reverse=True):
            if quantity > 0:
                summary_parts.append(f"{quantity} HK-${denomination.replace('元', '')}")
        
        # Add coins
        for denomination, quantity in sorted(total_coins.items(), 
                                           key=lambda x: self._extract_value(x[0]), 
                                           reverse=True):
            if quantity > 0:
                summary_parts.append(f"{quantity} HK-${denomination.replace('元', '')} coin")
        
        if summary_parts:
            lines.append(", ".join(summary_parts))
        else:
            lines.append("No currency detected")
        
        # Calculate total value
        total_value = 0
        for denomination, quantity in total_banknotes.items():
            total_value += self._extract_value(denomination) * quantity
        for denomination, quantity in total_coins.items():
            total_value += self._extract_value(denomination) * quantity
        
        lines.append(f"\nTotal Value: HK${total_value:.2f}")
        lines.append(f"Processed Images: {len(image_names)}")
        
        return "\n".join(lines)
    
    def _extract_currency_summary(self, analysis: Dict[str, Any]) -> str:
        """Extract a summary string from analysis results."""
        summary_parts = []
        
        # Process banknotes
        banknotes = analysis.get("紙幣", [])
        for item in banknotes:
            denomination = item.get("面額", "Unknown")
            quantity = item.get("數量", 0)
            if quantity > 0:
                summary_parts.append(f"{quantity} HK-${denomination.replace('元', '')}")
        
        # Process coins
        coins = analysis.get("硬幣", [])
        for item in coins:
            denomination = item.get("面額", "Unknown")
            quantity = item.get("數量", 0)
            if quantity > 0:
                summary_parts.append(f"{quantity} HK-${denomination.replace('元', '')} coin")
        
        return ", ".join(summary_parts) if summary_parts else ""
    
    def _extract_value(self, denomination: str) -> float:
        """Extract numeric value from denomination string."""
        try:
            return float(denomination.replace("元", "").replace("$", "").replace("HK", "").strip())
        except:
            return 0.0
    
    def _save_json_result(self, result: RecognitionResult, image_name: str, filepath: Path):
        """Save result as JSON."""
        data = {
            "image": image_name,
            "timestamp": datetime.now().isoformat(),
            "success": result.success,
            "analysis": result.analysis,
            "processing_time": result.processing_time,
            "config_used": result.config_used,
            "preprocessing_applied": result.preprocessing_applied,
            "image_info": result.image_info,
            "error_message": result.error_message
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)


def create_result_saver(output_dir: str = "test_results") -> ResultSaver:
    """
    Create a ResultSaver instance.
    
    Args:
        output_dir: Directory to save results
        
    Returns:
        ResultSaver instance
    """
    return ResultSaver(output_dir)