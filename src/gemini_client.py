"""
Gemini API client for Hong Kong currency recognition.
Provides configurable API access with different preset configurations.
"""

import google.genai as genai
from google.genai import types
from PIL import Image
import json
import os
import time
import logging
from typing import Dict, Any, Optional, Union, Generator, List
from dataclasses import dataclass, asdict
from pydantic import BaseModel, Field

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Pydantic models for structured response
class CurrencyItem(BaseModel):
    """Model for individual currency items (banknotes or coins)."""
    面額: str = Field()
    數量: int = Field()
    置信度: float = Field(ge=0.0, le=1.0)


class UnrecognizedInfo(BaseModel):
    """Model for unrecognized currency information."""
    原因: str = Field()
    可見特徵: str = Field()


class CurrencyResponse(BaseModel):
    """Complete currency recognition response model."""
    紙幣: List[CurrencyItem] = Field(default_factory=list)
    硬幣: List[CurrencyItem] = Field(default_factory=list)
    無法識別: Optional[UnrecognizedInfo] = Field(default=None)


@dataclass
class GeminiConfig:
    """Configuration class for Gemini API settings."""
    temperature: float = 0.1
    top_p: float = 0.8
    top_k: int = 10
    max_output_tokens: int = 1000
    candidate_count: int = 1
    
    
    # Response format
    response_mime_type: str = "application/json"
    
    # Additional settings
    enable_streaming: bool = False
    enable_web_search: bool = False
    


class GeminiClient:
    """Enhanced Gemini API client for currency recognition."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize Gemini client.
        
        Args:
            api_key: Gemini API key (will use GEMINI_API_KEY env var if not provided)
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable must be set or api_key provided")
        
        # Configure client
        self.client = genai.Client(api_key=self.api_key)
        
        # Load system prompt
        self.system_prompt = self._load_system_prompt()
        
        # Predefined configurations
        self.presets = self._create_presets()
        
        # Current configuration
        self.generation_config = None
        self.current_config = None
        self.model_name = "gemini-2.5-flash"
        
        logger.info("Gemini client initialized successfully")
    
    def _load_system_prompt(self) -> str:
        """Load the system prompt from prompt.md file."""
        try:
            prompt_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'prompt.md')
            with open(prompt_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            logger.warning("prompt.md not found, using basic prompt")
            return "You are a Hong Kong currency recognition system. Analyze the image and identify all banknotes and coins."
    
    def _create_presets(self) -> Dict[str, GeminiConfig]:
        """Create predefined configuration presets from config file."""
        presets = {}

        # Try to load from config file first
        try:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'config',
                'api_presets.json'
            )
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Convert config file presets to GeminiConfig objects
            for preset_name, preset_config in config_data.get('presets', {}).items():
                presets[preset_name] = GeminiConfig(
                    temperature=preset_config.get('temperature', 0.3),
                    top_p=preset_config.get('top_p', 0.9),
                    top_k=preset_config.get('top_k', 20),
                    max_output_tokens=preset_config.get('max_output_tokens', 800),
                    enable_streaming=preset_config.get('enable_streaming', False),
                    enable_web_search=preset_config.get('enable_web_search', False)
                )

        except FileNotFoundError:
            logger.warning("api_presets.json not found, using default presets")

        # Add default presets if not loaded from config
        default_presets = {
            'high_accuracy': GeminiConfig(
                temperature=0.1,
                top_p=0.8,
                top_k=10,
                max_output_tokens=2000,
                enable_streaming=False,
                enable_web_search=False
            ),
            'fast_response': GeminiConfig(
                temperature=0.5,
                top_p=0.95,
                top_k=40,
                max_output_tokens=2000,
                enable_streaming=False,
                enable_web_search=False
            ),
            'web_enhanced': GeminiConfig(
                temperature=0.2,
                top_p=0.85,
                top_k=15,
                max_output_tokens=2000,
                enable_streaming=False,
                enable_web_search=True
            )
        }

        # Add any missing default presets
        for preset_name, preset_config in default_presets.items():
            if preset_name not in presets:
                presets[preset_name] = preset_config

        return presets
    
    def configure_model(self, preset: str = 'high_accuracy', custom_config: Optional[GeminiConfig] = None):
        """
        Configure the model with a preset or custom configuration.
        
        Args:
            preset: Name of preset configuration
            custom_config: Custom GeminiConfig object (overrides preset)
        """
        if custom_config:
            self.current_config = custom_config
        elif preset in self.presets:
            self.current_config = self.presets[preset]
        else:
            raise ValueError(f"Unknown preset: {preset}. Available: {list(self.presets.keys())}")
        
        # Configure generation parameters
        config_params = {
            'temperature': self.current_config.temperature,
            'top_p': self.current_config.top_p,
            'top_k': self.current_config.top_k,
            'max_output_tokens': self.current_config.max_output_tokens,
            'candidate_count': self.current_config.candidate_count,
            'system_instruction': self.system_prompt
        }
        
        # Add Google Search grounding tool if enabled
        if self.current_config.enable_web_search:
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config_params['tools'] = [grounding_tool]
            # Grounding tools are incompatible with structured output
            config_params['response_mime_type'] = None
            logger.info("Google Search grounding tool enabled (structured output disabled for compatibility)")
        else:
            # Use structured output with response schema for better JSON parsing
            config_params['response_mime_type'] = 'application/json'
            config_params['response_schema'] = CurrencyResponse
            logger.info("Structured output enabled with CurrencyResponse schema")
        
        self.generation_config = types.GenerateContentConfig(**config_params)
        
        # Model name for API calls
        self.model_name = "gemini-2.5-flash"
        
        logger.info(f"Model configured with preset: {preset}")
        logger.debug(f"Configuration: {asdict(self.current_config)}")
    
    def analyze_image(self, image: Image.Image, 
                     additional_prompt: str = "",
                     timeout: int = 60,
                     max_retries: int = 3) -> Dict[str, Any]:
        """
        Analyze currency image using Gemini API.
        
        Args:
            image: PIL Image to analyze
            additional_prompt: Additional context for the analysis
            timeout: Request timeout in seconds
            
        Returns:
            Dictionary with analysis results
        """
        if not hasattr(self, 'generation_config') or not self.generation_config:
            self.configure_model()  # Use default configuration
        
        start_time = time.time()
        try:
            # Prepare the prompt
            prompt = additional_prompt if additional_prompt else "請分析此圖像中的香港貨幣。"
            
            # Retry logic for empty responses
            response = ""
            retry_count = 0
            
            while retry_count < max_retries:
                # Always use non-streaming for now
                response = self._analyze_without_streaming(image, prompt, timeout)
                
                if response:  # Got a non-empty response
                    break
                    
                retry_count += 1
                if retry_count < max_retries:
                    logger.warning(f"Empty response, retrying ({retry_count}/{max_retries})...")
                    time.sleep(1)  # Brief delay before retry
            
            processing_time = time.time() - start_time
            
            # Parse response based on whether grounding tools are enabled
            try:
                if not response:
                    logger.error("Empty response from API")
                    return {
                        "error": "Empty response from API",
                        "processing_time": processing_time
                    }
                
                # Check if grounding tools are enabled (no structured output)
                if self.current_config and self.current_config.enable_web_search:
                    # Handle markdown-formatted JSON (when grounding tools are used)
                    json_text = response
                    if response.strip().startswith('```json'):
                        # Extract JSON from markdown code block
                        start_marker = '```json'
                        end_marker = '```'
                        start_idx = response.find(start_marker) + len(start_marker)
                        end_idx = response.find(end_marker, start_idx)
                        if end_idx != -1:
                            json_text = response[start_idx:end_idx].strip()
                        else:
                            # If no closing marker, take everything after the opening
                            json_text = response[start_idx:].strip()
                    
                    # Parse the extracted JSON
                    result = json.loads(json_text)
                else:
                    # With structured output, response should already be valid JSON
                    result = json.loads(response)
                
                # Add metadata
                result['processing_time'] = processing_time
                if self.current_config:
                    result['model_config'] = asdict(self.current_config)
                
                return result
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {str(e)}")
                logger.debug(f"Response preview: {response[:500] if response else 'None'}")
                
                # For debugging, return the error with the raw response
                return {
                    "error": f"JSON parsing error: {str(e)}",
                    "raw_response": response[:1000] if response else None,  # Limit size
                    "processing_time": processing_time,
                    "note": "If using grounding tools, structured output is disabled. Consider disabling web search for better JSON reliability."
                }
                
        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    def _analyze_without_streaming(self, image: Image.Image, prompt: str, timeout: int) -> str:
        """Analyze image without streaming."""
        # Convert PIL Image to bytes
        import io
        image_bytes = io.BytesIO()
        
        # Handle RGBA images (PNG with transparency)
        if image.mode in ('RGBA', 'P'):
            # Convert to RGB for JPEG compatibility
            rgb_image = image.convert('RGB')
            rgb_image.save(image_bytes, format='JPEG')
        else:
            image.save(image_bytes, format='JPEG')
            
        image_bytes = image_bytes.getvalue()
        
        response = self.client.models.generate_content(
            model=self.model_name,
            contents=[
                types.Part.from_bytes(
                    data=image_bytes,
                    mime_type='image/jpeg',
                ),
                prompt
            ],
            config=self.generation_config
        )
        
        # Better response handling
        try:
            # Check if response has candidates
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'finish_reason'):
                    logger.debug(f"Finish reason: {candidate.finish_reason}")
                    
            # Try to get text
            text = response.text if hasattr(response, 'text') else ""
            
            # Ensure we always return a string
            if text is None:
                text = ""
            
            # Log response details
            logger.debug(f"Response text length: {len(text)}")
            if text:
                logger.debug(f"Response text preview: {text[:100]}...")
            else:
                logger.warning("Empty response text from API")
                # Log more details about the response
                if hasattr(response, 'candidates'):
                    logger.debug(f"Number of candidates: {len(response.candidates) if response.candidates else 0}")
                    
            return text
        except Exception as e:
            logger.error(f"Error extracting text from response: {e}")
            return ""
    
    def _analyze_with_streaming(self, image: Image.Image, prompt: str, timeout: int) -> str:
        """Analyze image with streaming response."""
        # Convert PIL Image to bytes
        import io
        image_bytes = io.BytesIO()
        
        # Handle RGBA images (PNG with transparency)
        if image.mode in ('RGBA', 'P'):
            # Convert to RGB for JPEG compatibility
            rgb_image = image.convert('RGB')
            rgb_image.save(image_bytes, format='JPEG')
        else:
            image.save(image_bytes, format='JPEG')
            
        image_bytes = image_bytes.getvalue()
        
        # For now, use non-streaming approach due to API issues
        # This avoids the empty response problem with streaming
        return self._analyze_without_streaming(image, prompt, timeout)
    
    def batch_analyze(self, images: list[Image.Image], 
                     prompts: Optional[list[str]] = None,
                     delay_between_requests: float = 1.0) -> list[Dict[str, Any]]:
        """
        Analyze multiple images in batch.
        
        Args:
            images: List of PIL Images
            prompts: List of prompts (one per image, optional)
            delay_between_requests: Delay in seconds between API calls
            
        Returns:
            List of analysis results
        """
        if prompts and len(prompts) != len(images):
            raise ValueError("Number of prompts must match number of images")
        
        results = []
        for i, image in enumerate(images):
            prompt = prompts[i] if prompts else ""
            
            logger.info(f"Analyzing image {i+1}/{len(images)}")
            result = self.analyze_image(image, prompt)
            results.append(result)
            
            # Add delay to respect API limits
            if i < len(images) - 1:
                time.sleep(delay_between_requests)
        
        return results
    
    def test_configuration(self, test_image: Image.Image) -> Dict[str, Any]:
        """
        Test current configuration with a sample image.
        
        Args:
            test_image: PIL Image for testing
            
        Returns:
            Test results including performance metrics
        """
        if not hasattr(self, 'generation_config') or not self.generation_config:
            self.configure_model()
        
        test_prompt = "This is a test. Please respond with a simple JSON object containing 'status': 'test_successful'."
        
        start_time = time.time()
        result = self.analyze_image(test_image, test_prompt)
        end_time = time.time()
        
        return {
            "config_name": "current",
            "response_time": end_time - start_time,
            "success": "error" not in result,
            "result": result,
            "configuration": asdict(self.current_config) if self.current_config else None
        }
    
    def benchmark_presets(self, test_image: Image.Image) -> Dict[str, Dict[str, Any]]:
        """
        Benchmark all preset configurations.
        
        Args:
            test_image: PIL Image for testing
            
        Returns:
            Dictionary with benchmark results for each preset
        """
        benchmark_results = {}
        original_config = self.current_config
        
        for preset_name in self.presets.keys():
            logger.info(f"Benchmarking preset: {preset_name}")
            
            try:
                self.configure_model(preset_name)
                result = self.test_configuration(test_image)
                benchmark_results[preset_name] = result
                
                # Small delay between tests
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"Error benchmarking {preset_name}: {e}")
                benchmark_results[preset_name] = {
                    "error": str(e),
                    "success": False
                }
        
        # Restore original configuration
        if original_config:
            self.current_config = original_config
            self._reconfigure_model()
        
        return benchmark_results
    
    def _reconfigure_model(self):
        """Reconfigure model with current configuration."""
        if self.current_config:
            config_params = {
                'temperature': self.current_config.temperature,
                'top_p': self.current_config.top_p,
                'top_k': self.current_config.top_k,
                'max_output_tokens': self.current_config.max_output_tokens,
                'candidate_count': self.current_config.candidate_count,
                'system_instruction': self.system_prompt
            }
            
            # Add Google Search grounding tool if enabled
            if self.current_config.enable_web_search:
                grounding_tool = types.Tool(google_search=types.GoogleSearch())
                config_params['tools'] = [grounding_tool]
                # Grounding tools are incompatible with structured output
                config_params['response_mime_type'] = None
            else:
                # Use structured output with response schema for better JSON parsing
                config_params['response_mime_type'] = 'application/json'
                config_params['response_schema'] = CurrencyResponse
            
            self.generation_config = types.GenerateContentConfig(**config_params)
            self.model_name = "gemini-2.5-flash"
    
    def get_available_presets(self) -> list[str]:
        """Get list of available preset names."""
        return list(self.presets.keys())
    
    def get_current_config(self) -> Optional[Dict[str, Any]]:
        """Get current configuration as dictionary."""
        return asdict(self.current_config) if self.current_config else None


# Convenience functions
def create_gemini_client(api_key: Optional[str] = None, preset: str = 'high_accuracy') -> GeminiClient:
    """
    Create and configure a Gemini client with specified preset.
    
    Args:
        api_key: Gemini API key
        preset: Configuration preset name
        
    Returns:
        Configured GeminiClient instance
    """
    client = GeminiClient(api_key)
    client.configure_model(preset)
    return client