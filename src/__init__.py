"""
Hong Kong Currency Recognition System

This package provides functionality for recognizing Hong Kong banknotes
using Google's Gemini 2.5 Flash API with advanced image processing.
"""

from .currency_recognizer import <PERSON>urrency<PERSON><PERSON><PERSON>ni<PERSON>, RecognitionResult
from .image_processor import ImageProcessor, process_image_for_recognition
from .gemini_client import Gemini<PERSON>lient, GeminiConfig

__version__ = "0.1.0"
__all__ = [
    "CurrencyRecognizer",
    "RecognitionResult", 
    "ImageProcessor",
    "process_image_for_recognition",
    "GeminiClient",
    "GeminiConfig"
]
