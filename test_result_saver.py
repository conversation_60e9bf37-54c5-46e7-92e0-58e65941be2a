#!/usr/bin/env python3
"""
Test script for the result saver functionality.
"""

import os
import sys
from src.currency_recognizer import create_currency_recognizer
from src.result_saver import create_result_saver


def test_single_image_save():
    """Test saving a single image result."""
    print("Testing single image result saving...")
    
    # Initialize components
    recognizer = create_currency_recognizer()
    result_saver = create_result_saver()
    
    # Test image
    test_image = "hkbanknote.jpeg"
    
    if os.path.exists(test_image):
        # Recognize currency
        result = recognizer.recognize_currency(
            test_image,
            preset_name="high_accuracy_local",
            enable_preprocessing=True
        )
        
        # Save in different formats
        print("\n1. Saving summary format...")
        summary_path = result_saver.save_single_result(
            result,
            test_image,
            format="summary"
        )
        print(f"   Saved to: {summary_path}")
        
        print("\n2. Saving detailed format...")
        detailed_path = result_saver.save_single_result(
            result,
            test_image,
            format="detailed"
        )
        print(f"   Saved to: {detailed_path}")
        
        print("\n3. Saving consolidated summary...")
        consolidated_path = result_saver.save_consolidated_summary(
            result,
            test_image
        )
        print(f"   Saved to: {consolidated_path}")
        
        # Display the consolidated summary
        with open(consolidated_path, 'r', encoding='utf-8') as f:
            print("\n" + "="*60)
            print("Consolidated Summary Content:")
            print("="*60)
            print(f.read())
    else:
        print(f"Test image {test_image} not found!")


def test_batch_save():
    """Test saving batch results."""
    print("\n\nTesting batch result saving...")
    
    # Initialize components
    recognizer = create_currency_recognizer()
    result_saver = create_result_saver()
    
    # Find test images
    test_images = []
    for i in range(1, 7):
        img_path = f"hkbanknote{i}.png" if i > 1 else "hkbanknote.jpeg"
        if os.path.exists(img_path):
            test_images.append(img_path)
    
    if len(test_images) >= 2:
        print(f"Found {len(test_images)} test images")
        
        # Process first 2 images for quick test
        results = []
        for img in test_images[:2]:
            print(f"Processing {img}...")
            result = recognizer.recognize_currency(
                img,
                preset_name="balanced_web",
                enable_preprocessing=True
            )
            results.append(result)
        
        # Save batch results
        batch_path = result_saver.save_batch_results(
            results,
            test_images[:2]
        )
        print(f"\nBatch results saved to: {batch_path}")
        
        # Display the batch results
        with open(batch_path, 'r', encoding='utf-8') as f:
            print("\n" + "="*60)
            print("Batch Results Content:")
            print("="*60)
            print(f.read())
    else:
        print("Not enough test images found for batch testing!")


if __name__ == "__main__":
    print("Result Saver Test Script")
    print("="*60)
    
    # Test single image
    test_single_image_save()
    
    # Test batch processing
    test_batch_save()
    
    print("\n\nTest completed! Check the test_results/ directory for saved files.")