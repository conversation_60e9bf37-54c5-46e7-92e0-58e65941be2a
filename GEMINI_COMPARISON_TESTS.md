# Gemini Client Comparison Test Suite

## Overview

I've created a comprehensive test suite to systematically compare the two Gemini client implementations (`gemini_client.py` and `gemini_client2.py`) for currency recognition using the Hong Kong banknote image. This test suite evaluates consistency, accuracy, and performance differences between the implementations.

## Key Differences Between Clients

### `gemini_client.py` (Client 1)
- **Structured Responses**: Uses Pydantic models (`CurrencyItem`, `UnrecognizedInfo`, `CurrencyResponse`)
- **Response Schema**: Implements `response_schema` for better JSON parsing
- **Validation**: Built-in response validation through Pydantic
- **Timeout**: Default 60 seconds
- **Output Tokens**: 2000 max tokens for most presets

### `gemini_client2.py` (Client 2)  
- **Simple Implementation**: Basic JSON response handling without Pydantic
- **Lighter Weight**: Fewer dependencies and simpler structure
- **Timeout**: Default 30 seconds
- **Output Tokens**: 1000 max tokens for most presets
- **Different Presets**: Includes 'balanced' preset not in Client 1

## Files Created

### 1. `tests/test_gemini_client_comparison.py`
**Main test suite** with comprehensive comparison functionality:

- **`GeminiClientComparator`**: Core class for running comparisons
- **`ComparisonResult`**: Data structure for storing individual test results
- **`TestSummary`**: Statistical summary of test runs
- **Multiple iteration testing**: Run same test multiple times for consistency analysis
- **Preset comparison**: Test across different API configurations
- **Statistical analysis**: Success rates, timing, consistency metrics
- **Error tracking**: Capture and analyze failure patterns
- **Unit tests**: pytest-compatible test functions

### 2. `run_gemini_comparison.py`
**User-friendly test runner** with interactive menu:

- Quick test (3 iterations per preset)
- Standard test (10 iterations per preset)  
- Comprehensive test (20 iterations per preset)
- Single preset testing
- Unit tests only
- Custom configuration options

### 3. `demo_comparison.py`
**Simple demonstration script** showing basic comparison functionality:

- Runs 3 quick iterations
- Shows real-time results
- Generates summary statistics
- Perfect for initial testing

### 4. `tests/README_comparison.md`
**Comprehensive documentation** covering:

- Usage instructions
- Command-line options
- Result interpretation guidelines
- Troubleshooting tips
- Example outputs

## Usage Examples

### Quick Start
```bash
# Interactive test runner (recommended)
python run_gemini_comparison.py

# Quick demo (3 iterations)
python demo_comparison.py

# Direct command line
python tests/test_gemini_client_comparison.py --quick
```

### Advanced Usage
```bash
# Test specific preset with custom iterations
python tests/test_gemini_client_comparison.py --preset high_accuracy --iterations 15

# Use different test image
python tests/test_gemini_client_comparison.py --image hkbanknote2.png

# Run unit tests
pytest tests/test_gemini_client_comparison.py -v
```

## Test Metrics

The test suite evaluates:

### Consistency
- **Results Matching**: How often both clients produce similar outputs
- **Target**: >80% consistency rate indicates good alignment

### Accuracy  
- **Success Rate**: Percentage of successful currency recognitions
- **Target**: >90% success rate indicates reliable performance

### Performance
- **Processing Time**: Average response time for each client
- **Comparison**: Identifies which client is faster and by how much

### Reliability
- **Error Patterns**: Types and frequency of failures
- **Stability**: Consistency across multiple runs

## Sample Output

```
GEMINI CLIENT COMPARISON TEST REPORT
================================================================================
Test Session: 20250714_123456
Test Image: hkbanknote.png

PRESET: HIGH_ACCURACY
--------------------------------------------------
Total Iterations: 10
Client 1 Success Rate: 9/10 (90.0%)
Client 2 Success Rate: 10/10 (100.0%)
Result Consistency: 8/10 (80.0%)
Avg Processing Time Client 1: 3.45s
Avg Processing Time Client 2: 3.12s
Performance: Client 2 is 0.33s faster on average

RECOMMENDATIONS:
✅ Good consistency rate between clients
✅ Similar performance between clients
```

## Key Features

### 1. **Statistical Analysis**
- Success rates and consistency metrics
- Performance comparisons with timing analysis
- Error rate tracking and categorization

### 2. **Flexible Testing**
- Multiple iteration support for reliability testing
- Preset comparison across different configurations
- Custom image and parameter support

### 3. **Comprehensive Reporting**
- Detailed JSON results for programmatic analysis
- Human-readable reports with recommendations
- Session tracking with timestamps

### 4. **Error Handling**
- Graceful failure handling with detailed error logging
- Timeout protection and rate limiting consideration
- Retry mechanisms for transient failures

### 5. **Easy Integration**
- pytest-compatible unit tests
- Command-line interface with multiple options
- Modular design for easy extension

## Prerequisites

- Python 3.8+
- Valid Gemini API key (`GEMINI_API_KEY` environment variable)
- Test image (`hkbanknote.png` in project root)
- Required dependencies (installed via `uv sync`)

## Results Storage

All test results are saved in the `test_results/` directory:

- **JSON files**: Raw detailed results with all iteration data
- **Report files**: Human-readable analysis and recommendations  
- **Timestamps**: All files include session timestamps for tracking

## Next Steps

1. **Run Initial Tests**: Start with `python demo_comparison.py` for a quick overview
2. **Comprehensive Analysis**: Use `python run_gemini_comparison.py` for full testing
3. **Review Results**: Analyze generated reports in `test_results/` directory
4. **Iterate**: Adjust configurations based on findings and re-test

This test suite provides a robust foundation for evaluating and comparing the Gemini client implementations, helping you make informed decisions about which client to use for different scenarios.
