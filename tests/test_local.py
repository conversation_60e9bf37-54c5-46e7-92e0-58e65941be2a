"""
Local image testing module for Hong Kong currency recognition.
Tests various configurations with local image files.
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from src package
from src.currency_recognizer import <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON><PERSON><PERSON>, RecognitionResult
from src.image_processor import ImageProcessor
from src.result_saver import create_result_saver

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LocalImageTester:
    """Test currency recognition with local images."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the local image tester.
        
        Args:
            api_key: Gemini API key
        """
        self.recognizer = CurrencyRecognizer(api_key)
        self.test_results = {}
        self.output_dir = "test_results"
        os.makedirs(self.output_dir, exist_ok=True)
        self.result_saver = create_result_saver(self.output_dir)
        
        # Create session timestamp for this test batch
        self.session_timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        logger.info("Local image tester initialized")
    
    def find_test_images(self, image_dir: str = ".") -> List[str]:
        """
        Find test images in the specified directory.
        
        Args:
            image_dir: Directory to search for images
            
        Returns:
            List of image file paths
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        image_files = []
        
        for file_path in Path(image_dir).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                image_files.append(str(file_path))
        
        logger.info(f"Found {len(image_files)} test images")
        return sorted(image_files)
    
    def test_single_image_all_configs(self, image_path: str) -> Dict[str, RecognitionResult]:
        """
        Test a single image with all available configurations.
        
        Args:
            image_path: Path to test image
            
        Returns:
            Dictionary with results for each configuration
        """
        logger.info(f"Testing image: {image_path}")
        
        # Load presets directly from config
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'api_presets.json')
        
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            presets = config_data.get('presets', {})
        except FileNotFoundError:
            logger.warning("Config file not found, using default presets")
            presets = {
                "high_accuracy_local": {"description": "High accuracy"},
                "plain": {"description": "Plain"},
                "fast_response": {"description": "Fast response"}
            }
        
        results = {}
        
        for preset_name, preset_config in presets.items():
            # Auto-detect preprocessing based on presence of image_preprocessing field
            preprocessing = 'image_preprocessing' in preset_config
            
            # Create a readable config name
            config_name = preset_name
            preset = preset_name
            
            logger.info(f"Running test: {config_name}")
            
            try:
                result = self.recognizer.recognize_currency(
                    image_path,
                    preset_name=preset,
                    enable_preprocessing=preprocessing,
                    save_processed_image=True,
                    output_dir=os.path.join(self.output_dir, "processed_images")
                )
                
                results[config_name] = result
                
                # Save individual test result
                self._save_individual_result(image_path, config_name, result)
                
                # Log result summary
                if result.success:
                    logger.info(f"✓ {config_name}: Success in {result.processing_time:.2f}s")
                else:
                    logger.error(f"✗ {config_name}: Failed - {result.error_message}")
                
                # Small delay between tests
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in test {config_name}: {e}")
                results[config_name] = RecognitionResult(
                    success=False,
                    analysis={},
                    processing_time=0,
                    config_used=preset,
                    preprocessing_applied=preprocessing,
                    image_info={},
                    error_message=str(e)
                )
        
        return results
    
    def test_preprocessing_comparison(self, image_path: str, preset: str = "high_accuracy_local") -> Dict[str, RecognitionResult]:
        """
        Compare results with and without preprocessing for a single image.
        
        Args:
            image_path: Path to test image
            preset: Configuration preset to use
            
        Returns:
            Comparison results
        """
        logger.info(f"Testing preprocessing effects on: {image_path}")
        
        return self.recognizer.compare_preprocessing_effects(image_path, preset)
    
    def batch_test_images(self, image_paths: List[str], preset: str = "high_accuracy_local") -> Dict[str, RecognitionResult]:
        """
        Test multiple images with the same configuration.
        
        Args:
            image_paths: List of image paths
            preset: Configuration preset to use
            
        Returns:
            Dictionary with results for each image
        """
        results = {}
        
        for image_path in image_paths:
            image_name = os.path.basename(image_path)
            logger.info(f"Testing {image_name} with {preset}")
            
            result = self.recognizer.recognize_currency(
                image_path,
                preset_name=preset,
                enable_preprocessing=True
            )
            
            results[image_name] = result
            time.sleep(1)  # Delay between API calls
        
        return results
    
    def comprehensive_test_suite(self, image_dir: str = ".") -> Dict[str, Any]:
        """
        Run comprehensive test suite on all images in directory.
        
        Args:
            image_dir: Directory containing test images
            
        Returns:
            Complete test results
        """
        test_images = self.find_test_images(image_dir)
        
        if not test_images:
            logger.warning(f"No test images found in {image_dir}")
            return {}
        
        logger.info(f"Running comprehensive test suite on {len(test_images)} images")
        
        comprehensive_results = {
            "test_summary": {
                "total_images": len(test_images),
                "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "test_directory": image_dir
            },
            "image_results": {},
            "configuration_performance": {},
            "preprocessing_comparison": {}
        }
        
        # Test each image with all configurations
        for image_path in test_images:
            image_name = os.path.basename(image_path)
            logger.info(f"Comprehensive testing: {image_name}")
            
            # All configurations test
            config_results = self.test_single_image_all_configs(image_path)
            comprehensive_results["image_results"][image_name] = config_results
            
            # Preprocessing comparison (use first image only to save time)
            if image_path == test_images[0]:
                preprocessing_results = self.test_preprocessing_comparison(image_path)
                comprehensive_results["preprocessing_comparison"][image_name] = preprocessing_results
        
        # Analyze configuration performance
        comprehensive_results["configuration_performance"] = self._analyze_performance(
            comprehensive_results["image_results"]
        )
        
        # Save results
        self._save_test_results(comprehensive_results, "comprehensive_test_results.json")
        
        # Save currency results in text format
        self._save_currency_results_text(comprehensive_results)
        
        # Save successful recognition results separately
        self._save_successful_recognitions(comprehensive_results)
        
        return comprehensive_results
    
    def _analyze_performance(self, image_results: Dict[str, Dict[str, RecognitionResult]]) -> Dict[str, Any]:
        """Analyze performance across different configurations."""
        config_stats = {}
        
        for image_name, config_results in image_results.items():
            for config_name, result in config_results.items():
                if config_name not in config_stats:
                    config_stats[config_name] = {
                        "success_count": 0,
                        "total_tests": 0,
                        "total_time": 0,
                        "average_time": 0,
                        "success_rate": 0
                    }
                
                stats = config_stats[config_name]
                stats["total_tests"] += 1
                stats["total_time"] += result.processing_time
                
                if result.success:
                    stats["success_count"] += 1
        
        # Calculate averages and rates
        for config_name, stats in config_stats.items():
            if stats["total_tests"] > 0:
                stats["average_time"] = stats["total_time"] / stats["total_tests"]
                stats["success_rate"] = stats["success_count"] / stats["total_tests"]
        
        return config_stats
    
    def _save_individual_result(self, image_path: str, config_name: str, result: RecognitionResult):
        """Save individual test result with Gemini response."""
        try:
            # Create timestamped directory for this test batch
            individual_results_dir = os.path.join(self.output_dir, f"individual_results_{self.session_timestamp}")
            os.makedirs(individual_results_dir, exist_ok=True)
            
            # Generate filename without timestamp (timestamp is in folder name)
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            filename = f"{image_name}_{config_name}.json"
            output_path = os.path.join(individual_results_dir, filename)
            
            # Prepare result data
            result_data = {
                "test_info": {
                    "image_path": image_path,
                    "config_name": config_name,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "success": result.success
                },
                "recognition_result": {
                    "analysis": result.analysis,
                    "processing_time": result.processing_time,
                    "config_used": result.config_used,
                    "preprocessing_applied": result.preprocessing_applied,
                    "error_message": result.error_message
                },
                "image_info": result.image_info
            }
            
            # Save to file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Individual result saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving individual result: {e}")
    
    def _save_test_results(self, results: Dict[str, Any], filename: str):
        """Save test results to JSON file."""
        try:
            output_path = os.path.join(self.output_dir, filename)
            
            # Convert RecognitionResult objects to dictionaries for JSON serialization
            serializable_results = self._make_serializable(results)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Test results saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving test results: {e}")
    
    def _save_currency_results_text(self, comprehensive_results: Dict[str, Any]):
        """Save currency recognition results in text format."""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"currency_results_{timestamp}.txt"
            output_path = os.path.join(self.output_dir, filename)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("Currency Recognition Test Results\n")
                f.write("================================\n")
                f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Test Directory: {comprehensive_results['test_summary'].get('test_directory', '.')}\n\n")
                
                # Process each image's results
                image_results = comprehensive_results.get("image_results", {})
                for image_name, config_results in image_results.items():
                    f.write(f"\nImage: {image_name}\n")
                    f.write("-" * 40 + "\n")
                    
                    # Find the best successful result
                    best_result = None
                    best_config = None
                    
                    for config_name, result in config_results.items():
                        if result.success and result.analysis:
                            best_result = result
                            best_config = config_name
                            break
                    
                    if best_result and best_result.analysis:
                        # Extract currency information
                        currency_parts = []
                        total_value = 0
                        
                        # Process banknotes
                        banknotes = best_result.analysis.get("紙幣", [])
                        for item in banknotes:
                            denomination = item.get("面額", "Unknown")
                            quantity = item.get("數量", 0)
                            if quantity > 0:
                                currency_parts.append(f"{quantity} HK-${denomination.replace('元', '')}")
                                try:
                                    value = float(denomination.replace("元", "")) * quantity
                                    total_value += value
                                except:
                                    pass
                        
                        # Process coins
                        coins = best_result.analysis.get("硬幣", [])
                        for item in coins:
                            denomination = item.get("面額", "Unknown")
                            quantity = item.get("數量", 0)
                            if quantity > 0:
                                currency_parts.append(f"{quantity} HK-${denomination.replace('元', '')} coin")
                                try:
                                    value = float(denomination.replace("元", "")) * quantity
                                    total_value += value
                                except:
                                    pass
                        
                        f.write(f"Configuration: {best_config}\n")
                        if currency_parts:
                            f.write(f"Result: {', '.join(currency_parts)}\n")
                            f.write(f"Total Value: HK${total_value:.2f}\n")
                        else:
                            # Check for unidentified items
                            unidentified = best_result.analysis.get("無法識別", {})
                            if unidentified:
                                f.write(f"Result: Unable to identify - {unidentified.get('原因', 'Unknown reason')}\n")
                            else:
                                f.write("Result: No currency detected\n")
                    else:
                        f.write("Result: All tests failed\n")
                
                # Add performance summary
                f.write("\n\nPerformance Summary\n")
                f.write("==================\n")
                performance = comprehensive_results.get("configuration_performance", {})
                for config_name, stats in performance.items():
                    f.write(f"{config_name}: Success Rate: {stats['success_rate']:.1%}, Avg Time: {stats['average_time']:.2f}s\n")
            
            logger.info(f"Currency results saved to {output_path}")
            
        except Exception as e:
            logger.error(f"Error saving currency results text: {e}")
    
    def _make_serializable(self, data: Any) -> Any:
        """Convert data to JSON-serializable format."""
        if isinstance(data, RecognitionResult):
            return {
                "success": data.success,
                "analysis": data.analysis,
                "processing_time": data.processing_time,
                "config_used": data.config_used,
                "preprocessing_applied": data.preprocessing_applied,
                "image_info": data.image_info,
                "error_message": data.error_message
            }
        elif isinstance(data, dict):
            return {key: self._make_serializable(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        else:
            return data
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """Generate a human-readable test report."""
        report = []
        report.append("=" * 80)
        report.append("LOCAL IMAGE CURRENCY RECOGNITION TEST REPORT")
        report.append("=" * 80)
        report.append("")
        
        # Test summary
        summary = results.get("test_summary", {})
        report.append(f"Test Date: {summary.get('test_timestamp', 'Unknown')}")
        report.append(f"Test Directory: {summary.get('test_directory', 'Unknown')}")
        report.append(f"Total Images Tested: {summary.get('total_images', 0)}")
        report.append("")
        
        # Configuration performance
        performance = results.get("configuration_performance", {})
        if performance:
            report.append("CONFIGURATION PERFORMANCE SUMMARY")
            report.append("-" * 40)
            
            for config_name, stats in performance.items():
                report.append(f"Configuration: {config_name}")
                report.append(f"  Success Rate: {stats['success_rate']:.1%}")
                report.append(f"  Average Time: {stats['average_time']:.2f}s")
                report.append(f"  Total Tests: {stats['total_tests']}")
                report.append("")
        
        # Preprocessing comparison
        preprocessing = results.get("preprocessing_comparison", {})
        if preprocessing:
            report.append("PREPROCESSING EFFECT ANALYSIS")
            report.append("-" * 40)
            
            for image_name, comparison in preprocessing.items():
                report.append(f"Image: {image_name}")
                
                no_prep = comparison.get("no_preprocessing")
                with_prep = comparison.get("with_preprocessing")
                
                if no_prep and with_prep:
                    report.append(f"  Without Preprocessing: {'✓' if no_prep.success else '✗'} ({no_prep.processing_time:.2f}s)")
                    report.append(f"  With Preprocessing: {'✓' if with_prep.success else '✗'} ({with_prep.processing_time:.2f}s)")
                    
                    if both_successful := (no_prep.success and with_prep.success):
                        # Compare confidence or other metrics if available
                        report.append("  Preprocessing improved results" if with_prep.processing_time < no_prep.processing_time * 1.5 else "  Preprocessing added processing time")
                
                report.append("")
        
        return "\n".join(report)
    
    def _save_successful_recognitions(self, comprehensive_results: Dict[str, Any]):
        """Save all successful recognition results in a separate file."""
        try:
            successful_results = {}
            
            # Extract successful results
            image_results = comprehensive_results.get("image_results", {})
            for image_name, config_results in image_results.items():
                successful_configs = {}
                for config_name, result in config_results.items():
                    if result.success and result.analysis:
                        successful_configs[config_name] = {
                            "analysis": result.analysis,
                            "processing_time": result.processing_time,
                            "config_used": result.config_used,
                            "preprocessing_applied": result.preprocessing_applied
                        }
                
                if successful_configs:
                    successful_results[image_name] = successful_configs
            
            # Save if there are any successful results
            if successful_results:
                filename = f"successful_recognitions_{self.session_timestamp}.json"
                output_path = os.path.join(self.output_dir, filename)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump({
                        "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                        "successful_recognitions": successful_results
                    }, f, indent=2, ensure_ascii=False)
                
                logger.info(f"Successful recognition results saved to {output_path}")
            else:
                logger.warning("No successful recognition results to save")
                
        except Exception as e:
            logger.error(f"Error saving successful recognitions: {e}")


def run_local_tests():
    """Main function to run local image tests."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Hong Kong currency recognition with local images")
    parser.add_argument("--image-dir", default=".", help="Directory containing test images")
    parser.add_argument("--single-image", help="Test a single image file")
    parser.add_argument("--preset", default="high_accuracy_local", help="Configuration preset to use")
    parser.add_argument("--api-key", help="Gemini API key (or set GEMINI_API_KEY env var)")
    
    args = parser.parse_args()
    
    try:
        # Get API key from args or environment
        api_key = args.api_key or os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("Error: No API key provided. Set GEMINI_API_KEY environment variable or use --api-key argument")
            return
        
        # Initialize tester
        tester = LocalImageTester(api_key)
        
        if args.single_image:
            # Test single image
            if not os.path.exists(args.single_image):
                print(f"Error: Image file {args.single_image} not found")
                return
            
            print(f"Testing single image: {args.single_image}")
            results = tester.test_single_image_all_configs(args.single_image)
            
            # Save single image test results
            image_name = os.path.basename(args.single_image)
            single_test_results = {
                "test_summary": {
                    "test_type": "single_image",
                    "image_path": args.single_image,
                    "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                },
                "results": results
            }
            
            # Save comprehensive results for single image
            filename = f"single_image_test_{tester.session_timestamp}.json"
            tester._save_test_results(single_test_results, filename)
            
            # Save successful recognitions
            tester._save_successful_recognitions({"image_results": {image_name: results}})
            
            # Print results
            for config_name, result in results.items():
                status = "✓ SUCCESS" if result.success else "✗ FAILED"
                print(f"{config_name}: {status} ({result.processing_time:.2f}s)")
                if result.error_message:
                    print(f"  Error: {result.error_message}")
                elif result.success and result.analysis:
                    print(f"  Recognition: {json.dumps(result.analysis, ensure_ascii=False)}")
        
        else:
            # Run comprehensive test suite
            print(f"Running comprehensive test suite on directory: {args.image_dir}")
            results = tester.comprehensive_test_suite(args.image_dir)
            
            # Generate and print report
            report = tester.generate_report(results)
            print(report)
            
            # Save report to file
            report_path = os.path.join(tester.output_dir, "test_report.txt")
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\nDetailed report saved to: {report_path}")
    
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    run_local_tests()