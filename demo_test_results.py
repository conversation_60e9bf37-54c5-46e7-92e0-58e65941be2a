#!/usr/bin/env python3
"""
Demo script to show how test results are saved in the new format.
This demonstrates the expected output without running actual API calls.
"""

import os
import time

# Create sample output file
output_dir = "test_results"
os.makedirs(output_dir, exist_ok=True)

timestamp = time.strftime("%Y%m%d_%H%M%S")
filename = f"currency_results_demo_{timestamp}.txt"
output_path = os.path.join(output_dir, filename)

sample_content = """Currency Recognition Test Results
================================
Timestamp: 2025-07-13 12:00:00
Test Directory: .

Image: hkbanknote.jpeg
----------------------------------------
Configuration: high_accuracy_local
Result: 1 HK-$100, 2 HK-$50, 5 HK-$20
Total Value: HK$300.00

Image: hkbanknote2.png
----------------------------------------
Configuration: balanced_web
Result: 3 HK-$500, 1 HK-$20
Total Value: HK$1520.00

Image: hkbanknote3.png
----------------------------------------
Configuration: high_accuracy_local
Result: 2 HK-$100, 4 HK-$10 coin
Total Value: HK$240.00

Image: hkbanknote4.png
----------------------------------------
Configuration: high_accuracy_local
Result: Unable to identify - 圖片模糊

Image: hkbanknote5.png
----------------------------------------
Configuration: web_enhanced
Result: 1 HK-$1000
Total Value: HK$1000.00

Image: hkbanknote6.png
----------------------------------------
Configuration: balanced_web
Result: 10 HK-$20, 5 HK-$5 coin
Total Value: HK$225.00


Performance Summary
==================
high_accuracy_local: Success Rate: 80.0%, Avg Time: 1.23s
balanced_web: Success Rate: 75.0%, Avg Time: 0.98s
web_enhanced: Success Rate: 70.0%, Avg Time: 1.55s
"""

with open(output_path, 'w', encoding='utf-8') as f:
    f.write(sample_content)

print(f"Demo test results saved to: {output_path}")
print("\nThis is how the test results will be saved when you run:")
print("- uv run python tests/test_local.py")
print("- uv run python tests/test_web.py --urls <image_url>")
print("\nThe actual results will contain the real currency detection from Gemini 2.5 Flash.")